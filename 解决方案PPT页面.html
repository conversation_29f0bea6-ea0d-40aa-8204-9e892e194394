<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>解决方案 - 多模态智能评测系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background: linear-gradient(135deg, #f8fffe 0%, #f0fdf4 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: auto;
        }

        .container {
            width: 100vw;
            min-width: 1400px;
            margin: 0;
            padding: 15px 30px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 顶部导航栏 */
        .header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            height: 60px;
        }

        .logo-section {
            background: linear-gradient(135deg, #4a5568, #2d3748);
            color: white;
            padding: 12px 25px;
            clip-path: polygon(0 0, calc(100% - 25px) 0, 100% 100%, 0 100%);
            margin-right: 20px;
        }

        .logo-text {
            font-size: 13px;
            font-weight: 500;
            letter-spacing: 1px;
        }

        .nav-tabs {
            display: flex;
            margin-left: auto;
        }

        .nav-tab {
            background: #10b981;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            font-size: 13px;
            font-weight: 500;
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .nav-tab:first-child {
            border-top-left-radius: 6px;
            border-bottom-left-radius: 6px;
        }

        .nav-tab:last-child {
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;
            border-right: none;
        }

        .nav-tab:hover {
            background: #059669;
            transform: translateY(-2px);
        }

        /* 主要内容区域 - 横向布局 */
        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            align-items: flex-start;
            height: calc(100vh - 120px);
            overflow-y: auto;
        }

        .left-content {
            padding-right: 20px;
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .right-content {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            position: sticky;
            top: 0;
        }

        /* 标题样式 - 横向优化 */
        .main-title {
            font-size: 36px;
            font-weight: 700;
            color: #065f46;
            margin-bottom: 15px;
            position: relative;
        }

        .main-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #10b981, #059669);
            border-radius: 2px;
        }

        /* 核心理念 - 紧凑布局 */
        .core-concept {
            margin-bottom: 25px;
        }

        .concept-text {
            font-size: 16px;
            color: #047857;
            line-height: 1.6;
            margin-bottom: 15px;
            text-align: center;
            font-weight: 500;
        }

        .highlight-box {
            background: #10b981;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            text-align: center;
            font-size: 14px;
            font-weight: 600;
            margin: 15px 0;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        /* 解决方案部分 - 紧凑布局 */
        .solution-section {
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 28px;
            font-weight: 700;
            color: #dc2626;
            margin-bottom: 10px;
        }

        .section-subtitle {
            font-size: 18px;
            color: #047857;
            margin-bottom: 20px;
            font-weight: 600;
        }

        /* 用户群体卡片 - 横向紧凑布局 */
        .user-groups {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .user-card {
            background: white;
            border-radius: 8px;
            padding: 18px;
            box-shadow: 0 3px 15px rgba(16, 185, 129, 0.1);
            border-left: 3px solid #10b981;
            transition: all 0.3s ease;
        }

        .user-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 25px rgba(16, 185, 129, 0.2);
        }

        .user-title {
            font-size: 16px;
            font-weight: 700;
            color: #065f46;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }

        .user-title::before {
            content: '';
            width: 6px;
            height: 6px;
            background: #10b981;
            border-radius: 50%;
            margin-right: 8px;
        }

        .user-description {
            font-size: 14px;
            color: #374151;
            line-height: 1.5;
        }

        /* 解决方案映射样式 */
        .solution-mapping {
            margin: 25px 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8fffe 0%, #f0fdf4 100%);
            border-radius: 15px;
            border: 1px solid #d1fae5;
        }

        .solution-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(16, 185, 129, 0.1);
            transition: all 0.3s ease;
        }

        .solution-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 20px rgba(16, 185, 129, 0.15);
        }

        .problem-tag {
            background: linear-gradient(135deg, #fee2e2, #fecaca);
            color: #dc2626;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 12px;
            min-width: 120px;
            text-align: center;
            border: 1px solid #fca5a5;
        }

        .solution-arrow {
            margin: 0 15px;
            font-size: 18px;
            color: #10b981;
            font-weight: bold;
        }

        .solution-tag {
            background: linear-gradient(135deg, #d1fae5, #a7f3d0);
            color: #065f46;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 12px;
            min-width: 120px;
            text-align: center;
            border: 1px solid #6ee7b7;
        }

        .solution-desc {
            margin-left: 15px;
            color: #374151;
            font-size: 13px;
            line-height: 1.4;
            flex: 1;
        }

        /* 手机展示图片 - 右侧固定 */
        .phone-display {
            position: relative;
            max-width: 280px;
            width: 100%;
            margin-bottom: 20px;
        }

        .phone-image {
            width: 100%;
            height: auto;
            border-radius: 15px;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .phone-image:hover {
            transform: scale(1.03);
        }

        /* 装饰元素 - 调整大小 */
        .decoration-circle {
            position: absolute;
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #10b981, #059669);
            border-radius: 50%;
            opacity: 0.1;
            top: -15px;
            right: -15px;
            z-index: -1;
        }

        /* 技术优势展示 - 横向紧凑 */
        .tech-advantages {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 6px 25px rgba(16, 185, 129, 0.1);
            border: 1px solid #d1fae5;
        }

        .advantages-title {
            font-size: 20px;
            font-weight: 700;
            color: #065f46;
            margin-bottom: 15px;
            text-align: center;
            position: relative;
        }

        .advantages-title::after {
            content: '';
            position: absolute;
            bottom: -6px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 2px;
            background: #10b981;
            border-radius: 2px;
        }

        .advantages-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 20px;
        }

        .advantage-item {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .advantage-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, #10b981, #059669);
        }

        .advantage-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.2);
        }

        .advantage-icon {
            width: 40px;
            height: 40px;
            background: #10b981;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            color: white;
            font-size: 20px;
        }

        .advantage-title {
            font-size: 14px;
            font-weight: 600;
            color: #065f46;
            margin-bottom: 6px;
        }

        .advantage-desc {
            font-size: 12px;
            color: #047857;
            line-height: 1.4;
        }

        /* 右侧内容区域布局 */
        .right-sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
            width: 100%;
        }

        /* 数据统计展示 - 右侧紧凑 */
        .stats-section {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border-radius: 12px;
            padding: 20px;
            color: white;
            text-align: center;
        }

        .stats-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 15px;
            opacity: 0.95;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
            display: block;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.9;
            font-weight: 500;
        }

        /* 流程步骤 - 右侧垂直布局 */
        .process-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(16, 185, 129, 0.1);
            border: 1px solid #d1fae5;
        }

        .process-title {
            font-size: 18px;
            font-weight: 700;
            color: #065f46;
            margin-bottom: 20px;
            text-align: center;
        }

        .process-steps {
            display: flex;
            flex-direction: column;
            gap: 15px;
            position: relative;
        }

        .process-steps::before {
            content: '';
            position: absolute;
            left: 30px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #d1fae5;
            z-index: 1;
        }

        .process-step {
            background: white;
            border: 2px solid #10b981;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .process-step:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
        }

        .step-number {
            font-size: 14px;
            font-weight: 700;
            color: #10b981;
            margin-bottom: 2px;
        }

        .step-icon {
            font-size: 12px;
            color: #047857;
        }

        .process-item {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .step-label {
            font-size: 14px;
            color: #047857;
            font-weight: 600;
        }

        .step-desc {
            font-size: 11px;
            color: #6b7280;
            margin-top: 4px;
            line-height: 1.3;
        }

        /* 横向布局响应式设计 */
        @media (max-width: 1400px) {
            .container {
                min-width: 1200px;
            }

            .main-content {
                grid-template-columns: 1.8fr 1fr;
                gap: 30px;
            }
        }

        @media (max-width: 1200px) {
            .container {
                min-width: 1000px;
                padding: 10px 20px;
            }

            .main-content {
                grid-template-columns: 1.5fr 1fr;
                gap: 25px;
            }

            .main-title {
                font-size: 32px;
            }

            .section-title {
                font-size: 24px;
            }

            .advantages-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }
        }

        @media (max-width: 1000px) {
            body {
                overflow-x: scroll;
            }

            .container {
                min-width: 900px;
            }

            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
                height: auto;
            }

            .right-content {
                position: static;
                flex-direction: row;
                justify-content: space-around;
                gap: 20px;
            }

            .phone-display {
                max-width: 200px;
            }

            .right-sidebar {
                flex-direction: row;
                gap: 15px;
            }

            .stats-section,
            .process-section {
                flex: 1;
                min-width: 200px;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes countUp {
            from {
                opacity: 0;
                transform: scale(0.5);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .user-card {
            animation: fadeInUp 0.6s ease forwards;
        }

        .user-card:nth-child(1) { animation-delay: 0.1s; }
        .user-card:nth-child(2) { animation-delay: 0.2s; }
        .user-card:nth-child(3) { animation-delay: 0.3s; }

        .left-content {
            animation: slideInLeft 0.8s ease forwards;
        }

        .right-content {
            animation: slideInRight 0.8s ease forwards;
        }

        .advantage-item {
            animation: fadeInUp 0.6s ease forwards;
        }

        .advantage-item:nth-child(1) { animation-delay: 0.1s; }
        .advantage-item:nth-child(2) { animation-delay: 0.2s; }
        .advantage-item:nth-child(3) { animation-delay: 0.3s; }
        .advantage-item:nth-child(4) { animation-delay: 0.4s; }

        .stat-number {
            animation: countUp 0.8s ease forwards;
        }

        .process-step {
            animation: fadeInUp 0.6s ease forwards;
        }

        .process-step:nth-child(1) { animation-delay: 0.2s; }
        .process-step:nth-child(2) { animation-delay: 0.4s; }
        .process-step:nth-child(3) { animation-delay: 0.6s; }
        .process-step:nth-child(4) { animation-delay: 0.8s; }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏 -->
        <div class="header">
            <div class="logo-section">
                <div class="logo-text">TEXT COMPANY NAME & LOGO</div>
            </div>
            <div class="nav-tabs">
                <a href="#" class="nav-tab">项目概述</a>
                <a href="#" class="nav-tab">项目内容</a>
                <a href="#" class="nav-tab">项目发展</a>
                <a href="#" class="nav-tab">团队介绍</a>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <div class="left-content">
                <!-- 主标题 -->
                <h1 class="main-title">解决方案</h1>

                <!-- 核心理念 -->
                <div class="core-concept">
                    <p class="concept-text">
                        核心理念：从"单点评估"走向"全景洞察"，从"结果评判"走向"过程赋能"。
                    </p>
                    <div class="highlight-box">
                        科大讯飞AI平台12个核心接口深度集成
                    </div>
                </div>

                <!-- 解决方案改革 -->
                <div class="solution-section">
                    <h2 class="section-title">解决方案</h2>
                    <h3 class="section-subtitle">改革</h3>

                    <!-- 痛点对应解决方案 -->
                    <div class="solution-mapping">
                        <div class="solution-item">
                            <div class="problem-tag">主观性强90%</div>
                            <div class="solution-arrow">→</div>
                            <div class="solution-tag">AI客观评测95%</div>
                            <p class="solution-desc">基于科大讯飞多模态AI算法，消除人为偏见，实现标准化客观评测</p>
                        </div>

                        <div class="solution-item">
                            <div class="problem-tag">效率低下70%</div>
                            <div class="solution-arrow">→</div>
                            <div class="solution-tag">效率提升300%</div>
                            <p class="solution-desc">自动化简历筛选、智能面试安排、实时评测报告生成</p>
                        </div>

                        <div class="solution-item">
                            <div class="problem-tag">标准不统一85%</div>
                            <div class="solution-arrow">→</div>
                            <div class="solution-tag">统一标准98%</div>
                            <p class="solution-desc">建立统一评测模型，确保不同岗位、不同面试官的评价一致性</p>
                        </div>
                    </div>

                    <!-- 用户群体价值主张 -->
                    <div class="user-groups">
                        <div class="user-card">
                            <h4 class="user-title">对于求职者</h4>
                            <p class="user-description">
                                提供一个公平、沉浸、个性化的自我展示与能力提升平台。获得详细的能力分析报告和改进建议。
                            </p>
                        </div>

                        <div class="user-card">
                            <h4 class="user-title">对于企业</h4>
                            <p class="user-description">
                                提供一个高效、精准、数据驱动的智能人才筛选工具。降低招聘成本60%，提升匹配精度。
                            </p>
                        </div>

                        <div class="user-card">
                            <h4 class="user-title">对于面试官</h4>
                            <p class="user-description">
                                提供一个智能、标准化的面试辅助与决策支持系统。减少主观判断，提升面试质量。
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 技术优势展示 -->
                <div class="tech-advantages">
                    <h3 class="advantages-title">技术优势</h3>
                    <div class="advantages-grid">
                        <div class="advantage-item">
                            <div class="advantage-icon">🧠</div>
                            <div class="advantage-title">AI深度集成</div>
                            <div class="advantage-desc">科大讯飞12个核心API接口，覆盖语音识别、情感分析、人脸识别、星火大模型等全维度AI能力</div>
                        </div>
                        <div class="advantage-item">
                            <div class="advantage-icon">🔄</div>
                            <div class="advantage-title">多模态融合</div>
                            <div class="advantage-desc">语音+视频+文本三维度智能分析，消除单一维度评估局限性，准确率提升35%</div>
                        </div>
                        <div class="advantage-item">
                            <div class="advantage-icon">⚡</div>
                            <div class="advantage-title">实时处理</div>
                            <div class="advantage-desc">毫秒级响应，支持千人并发面试，系统延迟<100ms，确保流畅体验</div>
                        </div>
                        <div class="advantage-item">
                            <div class="advantage-icon">🛡️</div>
                            <div class="advantage-title">安全可靠</div>
                            <div class="advantage-desc">AES-256加密，HTTPS传输，等保三级认证，全方位保护用户隐私数据</div>
                        </div>
                        <div class="advantage-item">
                            <div class="advantage-icon">📊</div>
                            <div class="advantage-title">数据驱动</div>
                            <div class="advantage-desc">基于大数据分析的人才画像，提供精准的岗位匹配和能力评估报告</div>
                        </div>
                        <div class="advantage-item">
                            <div class="advantage-icon">🎯</div>
                            <div class="advantage-title">个性化定制</div>
                            <div class="advantage-desc">支持不同行业、岗位的定制化评测标准，满足企业个性化需求</div>
                        </div>
                    </div>
                </div>


            </div>

            <div class="right-content">
                <!-- 手机展示图片 -->
                <div class="phone-display">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjUwMCIgdmlld0JveD0iMCAwIDMwMCA1MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iNTAwIiByeD0iMzAiIGZpbGw9InVybCgjZ3JhZGllbnQwX2xpbmVhcl8xXzEpIi8+CjxyZWN0IHg9IjIwIiB5PSI4MCIgd2lkdGg9IjI2MCIgaGVpZ2h0PSIzNDAiIHJ4PSIxNSIgZmlsbD0iIzEwYjk4MSIvPgo8Y2lyY2xlIGN4PSIxNTAiIGN5PSI0NTAiIHI9IjMwIiBmaWxsPSIjZmZmZmZmIiBmaWxsLW9wYWNpdHk9IjAuMyIvPgo8Y2lyY2xlIGN4PSIxNTAiIGN5PSI0MCIgcng9IjQwIiByeT0iOCIgZmlsbD0iIzAwMDAwMCIgZmlsbC1vcGFjaXR5PSIwLjMiLz4KPHN2ZyB4PSI0MCIgeT0iMTAwIiB3aWR0aD0iMjIwIiBoZWlnaHQ9IjMwMCI+CjxyZWN0IHdpZHRoPSIyMjAiIGhlaWdodD0iNjAiIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4yIiByeD0iOCIvPgo8cmVjdCB5PSI4MCIgd2lkdGg9IjIyMCIgaGVpZ2h0PSI0MCIgZmlsbD0iI2ZmZmZmZiIgZmlsbC1vcGFjaXR5PSIwLjE1IiByeD0iOCIvPgo8cmVjdCB5PSIxNDAiIHdpZHRoPSIyMjAiIGhlaWdodD0iNDAiIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4xNSIgcng9IjgiLz4KPHJlY3QgeT0iMjAwIiB3aWR0aD0iMjIwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjZmZmZmZmIiBmaWxsLW9wYWNpdHk9IjAuMTUiIHJ4PSI4Ii8+CjxyZWN0IHk9IjI2MCIgd2lkdGg9IjE2MCIgaGVpZ2h0PSIzMCIgZmlsbD0iIzA1OTY2OSIgcng9IjE1Ii8+Cjwvc3ZnPgo8L3N2Zz4KPGRlZnM+CjxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQwX2xpbmVhcl8xXzEiIHgxPSIwIiB5MT0iMCIgeDI9IjMwMCIgeTI9IjUwMCIgZ3JhZGllbnRVbml0cz0idXNlclNwYWNlT25Vc2UiPgo8c3RvcCBzdG9wLWNvbG9yPSIjNjNiM2VkIi8+CjxzdG9wIG9mZnNldD0iMSIgc3RvcC1jb2xvcj0iIzM3ODNmNCIvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPgo="
                         alt="智能面试系统手机界面"
                         class="phone-image">
                    <div class="decoration-circle"></div>
                </div>

                <!-- 右侧边栏内容 -->
                <div class="right-sidebar">
                    <!-- 数据统计展示 -->
                    <div class="stats-section">
                        <h3 class="stats-title">解决方案效果数据</h3>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="stat-number">95%</span>
                                <span class="stat-label">客观评测准确率</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">300%</span>
                                <span class="stat-label">招聘效率提升</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">60%</span>
                                <span class="stat-label">人力成本降低</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">98%</span>
                                <span class="stat-label">评测标准一致性</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">12+</span>
                                <span class="stat-label">AI接口集成</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">80%</span>
                                <span class="stat-label">时间成本节省</span>
                            </div>
                        </div>
                    </div>

                    <!-- 实施流程 -->
                    <div class="process-section">
                        <h3 class="process-title">解决方案实施流程</h3>
                        <div class="process-steps">
                            <div class="process-item">
                                <div class="process-step">
                                    <div class="step-number">1</div>
                                    <div class="step-icon">📋</div>
                                </div>
                                <div class="step-label">痛点诊断</div>
                                <div class="step-desc">深入分析企业招聘痛点，制定针对性解决方案</div>
                            </div>
                            <div class="process-item">
                                <div class="process-step">
                                    <div class="step-number">2</div>
                                    <div class="step-icon">🎯</div>
                                </div>
                                <div class="step-label">方案定制</div>
                                <div class="step-desc">基于行业特点和岗位需求，定制评测标准和流程</div>
                            </div>
                            <div class="process-item">
                                <div class="process-step">
                                    <div class="step-number">3</div>
                                    <div class="step-icon">🚀</div>
                                </div>
                                <div class="step-label">系统部署</div>
                                <div class="step-desc">快速部署多端应用，集成科大讯飞AI能力</div>
                            </div>
                            <div class="process-item">
                                <div class="process-step">
                                    <div class="step-number">4</div>
                                    <div class="step-icon">📊</div>
                                </div>
                                <div class="step-label">效果验证</div>
                                <div class="step-desc">持续监控效果数据，优化评测模型和流程</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加JavaScript增强交互 -->
    <script>
        // 数字动画效果
        function animateNumbers() {
            const numbers = document.querySelectorAll('.stat-number');
            numbers.forEach(number => {
                const target = number.textContent;
                const isPercentage = target.includes('%');
                const isTime = target.includes('ms');
                const isPlus = target.includes('+');

                let finalValue = parseInt(target.replace(/[^\d]/g, ''));
                let current = 0;
                const increment = finalValue / 50;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= finalValue) {
                        current = finalValue;
                        clearInterval(timer);
                    }

                    let displayValue = Math.floor(current);
                    if (isPercentage) displayValue += '%';
                    if (isTime) displayValue += 'ms';
                    if (isPlus) displayValue += '+';

                    number.textContent = displayValue;
                }, 50);
            });
        }

        // 页面加载完成后执行动画
        window.addEventListener('load', () => {
            setTimeout(animateNumbers, 1000);
        });

        // 添加滚动视差效果
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.decoration-circle');
            if (parallax) {
                const speed = scrolled * 0.5;
                parallax.style.transform = `translateY(${speed}px)`;
            }
        });

        // 添加鼠标跟随效果
        document.addEventListener('mousemove', (e) => {
            const cursor = document.querySelector('.decoration-circle');
            if (cursor) {
                const x = e.clientX / window.innerWidth;
                const y = e.clientY / window.innerHeight;
                cursor.style.transform = `translate(${x * 20}px, ${y * 20}px)`;
            }
        });

        // 添加卡片点击效果
        document.querySelectorAll('.user-card, .advantage-item').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // 添加导航标签点击效果
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>

    <style>
        /* 添加活跃状态样式 */
        .nav-tab.active {
            background: #059669 !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(5, 150, 105, 0.4);
        }

        /* 添加光标样式 */
        .user-card, .advantage-item, .process-step {
            cursor: pointer;
        }

        /* 添加选中效果 */
        .user-card:active, .advantage-item:active {
            transform: scale(0.95) !important;
        }
    </style>
</body>
</html>
